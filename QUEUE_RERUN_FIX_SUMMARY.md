# RunSim GUI 任务队列重新执行按钮修复总结

## 问题描述

当任务队列功能启用后，存在以下不一致行为：
1. 从运行参数配置页面点击"执行仿真和编译"按钮 → 任务正确添加到队列中
2. 从执行日志面板点击"重新执行"按钮 → 如果该用例最初没有通过队列执行，重新执行时不会添加到任务队列中，而是直接执行

## 修复目标

确保当任务队列功能启用时，无论是从配置页面的"执行仿真和编译"按钮还是从执行日志面板的"重新执行"按钮触发的任务，都应该统一添加到任务队列中进行管理，确保执行行为的一致性。

## 修复内容

### 1. 状态管理器增强 (`utils/state_manager.py`)

**修改内容：**
- 在 `ExecutionStatus` 枚举中添加了 `QUEUED` 状态
- 支持队列等待状态的管理

```python
class ExecutionStatus(Enum):
    """执行状态枚举"""
    READY = intern_string("ready")
    RUNNING = intern_string("running")
    PAUSED = intern_string("paused")
    FINISHED = intern_string("finished")
    STOPPED = intern_string("stopped")
    ERROR = intern_string("error")
    QUEUED = intern_string("queued")  # 新增
```

### 2. 日志面板核心修复 (`views/log_panel.py`)

**修改内容：**

#### 2.1 重构 `start_execution()` 方法
- 添加队列模式检查逻辑
- 根据队列状态选择执行方式

```python
def start_execution(self):
    """开始执行用例"""
    try:
        # 检查是否启用队列模式，如果启用则添加到队列而不是直接执行
        if self._should_use_queue():
            self._add_to_queue()
            return
        
        # 直接执行模式
        self._start_direct_execution()
            
    except Exception as e:
        print(f"启动执行时出错: {str(e)}")
        self.state_manager.set_execution_status(ExecutionStatus.ERROR)
```

#### 2.2 新增 `_should_use_queue()` 方法
- 检查当前是否启用队列模式
- 通过应用程序实例获取执行控制器状态

```python
def _should_use_queue(self):
    """检查是否应该使用队列模式"""
    try:
        # 通过QApplication获取执行控制器的队列模式状态
        app = QApplication.instance()
        if app:
            for widget in app.allWidgets():
                if hasattr(widget, 'app_controller'):
                    app_controller = widget.app_controller
                    if hasattr(app_controller, 'execution_controller'):
                        execution_controller = app_controller.execution_controller
                        return getattr(execution_controller, 'queue_mode_enabled', False)
        
        return False
    except Exception as e:
        print(f"检查队列模式状态时出错: {str(e)}")
        return False
```

#### 2.3 新增 `_add_to_queue()` 方法
- 将任务添加到队列
- 更新UI状态显示

```python
def _add_to_queue(self):
    """将任务添加到队列"""
    try:
        # 获取执行控制器
        app = QApplication.instance()
        execution_controller = None
        
        if app:
            for widget in app.allWidgets():
                if hasattr(widget, 'app_controller'):
                    app_controller = widget.app_controller
                    if hasattr(app_controller, 'execution_controller'):
                        execution_controller = app_controller.execution_controller
                        break
        
        if execution_controller:
            # 使用执行控制器的队列执行方法
            execution_controller.execute_command_with_queue(self.command, self.case_name)
            
            # 更新状态显示
            self.state_manager.set_execution_status(ExecutionStatus.QUEUED)
            self.log_text.clear()
            self.log_text.append("任务已添加到队列，等待执行...")
            
            # 显示命令预览
            self.cmd_preview.clear()
            command_text = intern_string(f"队列任务命令:\n{self.command}")
            self.cmd_preview.setText(command_text)
        else:
            # 如果无法获取执行控制器，回退到直接执行
            print("无法获取执行控制器，回退到直接执行模式")
            self._start_direct_execution()
            
    except Exception as e:
        print(f"添加任务到队列时出错: {str(e)}")
        # 出错时回退到直接执行
        self._start_direct_execution()
```

#### 2.4 新增 `_start_direct_execution()` 方法
- 保持原有的直接执行逻辑
- 确保向后兼容性

#### 2.5 更新状态消息映射
- 添加队列状态的显示文本

```python
status_messages = {
    ExecutionStatus.READY.value: intern_string("状态: 准备执行"),
    ExecutionStatus.RUNNING.value: intern_string("状态: 执行中..."),
    ExecutionStatus.PAUSED.value: intern_string("状态: 已暂停"),
    ExecutionStatus.FINISHED.value: intern_string("状态: 执行完成"),
    ExecutionStatus.STOPPED.value: intern_string("状态: 已终止"),
    ExecutionStatus.ERROR.value: intern_string("状态: 执行失败"),
    ExecutionStatus.QUEUED.value: intern_string("状态: 队列等待中...")  # 新增
}
```

#### 2.6 更新按钮状态逻辑
- 在队列状态下禁用重新执行按钮

```python
def _update_button_states(self):
    """根据当前状态更新按钮状态"""
    # ...
    is_queued = current_status == ExecutionStatus.QUEUED

    # 重新执行按钮：在非运行状态且非队列状态时才启用
    self.re_run_btn.setEnabled(not is_running and not is_queued)
    # ...
```

## 修复逻辑流程

```mermaid
flowchart TD
    A[用户点击重新执行按钮] --> B[LogPanel.start_execution()]
    B --> C{检查队列模式是否启用}
    C -->|是| D[_add_to_queue()]
    C -->|否| E[_start_direct_execution()]
    
    D --> F[获取执行控制器]
    F --> G{获取成功?}
    G -->|是| H[调用execute_command_with_queue()]
    G -->|否| I[回退到直接执行]
    
    H --> J[设置状态为QUEUED]
    J --> K[显示队列等待消息]
    
    E --> L[原有直接执行逻辑]
    I --> L
    
    L --> M[设置状态为RUNNING]
    M --> N[启动进程执行]
```

## 测试验证

### 自动验证
运行验证脚本：
```bash
python verify_queue_fix.py
```

### 手动测试步骤
1. 启动RunSim GUI: `python runsim_gui.py`
2. 在菜单栏中启用任务队列功能
3. 从配置页面执行一个任务，观察是否添加到队列
4. 在执行日志面板中点击"重新执行"按钮
5. 验证重新执行的任务也被添加到队列中
6. 禁用任务队列功能，再次测试重新执行是否直接执行

## 修复效果

✅ **问题解决：** 重新执行按钮现在与配置页面执行按钮行为完全一致
✅ **队列模式启用时：** 重新执行任务添加到队列
✅ **队列模式禁用时：** 重新执行任务直接执行
✅ **向后兼容：** 保持原有功能不受影响
✅ **错误处理：** 添加了完善的异常处理和回退机制
✅ **UI一致性：** 队列状态有相应的UI反馈

## 文件修改清单

- `utils/state_manager.py` - 添加QUEUED状态
- `views/log_panel.py` - 核心修复逻辑
- `verify_queue_fix.py` - 验证脚本（新增）
- `QUEUE_RERUN_FIX_SUMMARY.md` - 修复总结文档（本文件）
