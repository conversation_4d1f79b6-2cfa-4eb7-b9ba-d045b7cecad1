{"tasks": [{"task_id": "d9df5d1a-0e16-4f25-907b-0a1900006bd4", "name": "apcpu_memset_test_top", "command": "runsim -base top -block udtb/usvp -case apcpu_memset_test_top", "priority": "NORMAL", "status": "queued", "dependencies": [], "max_retries": 3, "retry_count": 0, "created_time": "2025-07-21T14:44:49.351944", "queued_time": "2025-07-21T14:44:49.351944", "start_time": null, "end_time": null, "exit_code": null, "error_message": "", "progress": 0.0, "metadata": {"case_name": "apcpu_memset_test_top", "work_directory": "E:\\doc\\python\\runsim", "log_file": "", "estimated_duration": 0, "tags": ["runsim"], "user_data": {}}}], "config": {"max_concurrent_tasks": 3, "scheduling_interval": 500, "auto_retry_failed": false, "max_retry_count": 5, "save_queue_state": true, "queue_state_file": "task_queue_state.json", "auto_save_interval": 30000, "max_memory_mb": 4096, "max_cpu_percent": 80, "resource_check_interval": 5000, "enable_task_logging": true, "log_level": "INFO", "log_file": "task_queue.log", "max_log_size_mb": 10, "auto_refresh_interval": 2000, "show_completed_tasks": true, "max_displayed_tasks": 1000, "enable_notifications": true, "notify_on_completion": true, "notify_on_failure": true, "enable_task_dependencies": true, "enable_priority_scheduling": true, "cleanup_completed_after_hours": 24}, "saved_time": "2025-07-21T14:44:57.948910"}