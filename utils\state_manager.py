"""
状态管理器 - 管理LogPanel的各种状态变量
实现观察者模式，提供状态变化通知
集成字符串池优化内存使用
"""
import time
from PyQt5.QtCore import QObject, pyqtSignal
from enum import Enum
from .string_pool import intern_string
from .signal_safety import safe_disconnect_multiple


class ExecutionStatus(Enum):
    """执行状态枚举"""
    READY = intern_string("ready")
    RUNNING = intern_string("running")
    PAUSED = intern_string("paused")
    FINISHED = intern_string("finished")
    STOPPED = intern_string("stopped")
    ERROR = intern_string("error")
    QUEUED = intern_string("queued")


class StateManager(QObject):
    """
    状态管理器，管理LogPanel的各种状态
    
    职责：
    1. 统一管理所有状态变量
    2. 提供状态变化通知（观察者模式）
    3. 状态历史记录和回滚
    4. 状态验证和约束
    """
    
    # 信号定义
    state_changed = pyqtSignal(str, object, object)  # 状态名, 旧值, 新值
    execution_status_changed = pyqtSignal(str, str)  # 旧状态, 新状态
    visibility_changed = pyqtSignal(bool, bool)  # 旧可见性, 新可见性
    
    def __init__(self, parent=None):
        """
        初始化状态管理器
        
        Args:
            parent (QObject): 父对象
        """
        super().__init__(parent)
        
        # 核心状态
        self._states = {
            'execution_status': ExecutionStatus.READY,
            'visibility': True,
            'auto_scroll': True,
            'log_completed': False,
            'process_id': None,
            'start_time': None,
            'end_time': None,
            'exit_code': None
        }
        
        # 扩展状态
        self._extended_states = {
            'last_output_time': None,
            'last_flush_time': None,
            'pending_flush': False,
            'flush_lock': False,
            'force_flush_needed': False,
            'timer_active': False
        }
        
        # 观察者列表
        self._observers = []
        
        # 状态历史（用于回滚）
        self._state_history = []
        self._max_history_size = 50
        
        # 状态约束
        self._state_constraints = {
            'execution_status': self._validate_execution_status,
            'visibility': self._validate_boolean,
            'auto_scroll': self._validate_boolean,
            'log_completed': self._validate_boolean
        }
        
        # 统计信息
        self._stats = {
            'total_state_changes': 0,
            'state_change_frequency': {},
            'last_change_time': time.time()
        }

        # 清理状态标志
        self._cleaned_up = False
    
    def add_observer(self, observer):
        """
        添加状态观察者
        
        Args:
            observer: 观察者对象，需要实现 on_state_changed 方法
        """
        if observer not in self._observers:
            self._observers.append(observer)
    
    def remove_observer(self, observer):
        """
        移除状态观察者
        
        Args:
            observer: 要移除的观察者对象
        """
        if observer in self._observers:
            self._observers.remove(observer)
    
    def set_state(self, key, value, notify=True, record_history=True):
        """
        设置状态值
        
        Args:
            key (str): 状态键
            value: 状态值
            notify (bool): 是否通知观察者
            record_history (bool): 是否记录历史
            
        Returns:
            bool: 设置是否成功
        """
        # 检查状态是否存在
        if key not in self._states and key not in self._extended_states:
            print(f"未知的状态键: {key}")
            return False
        
        # 获取当前值
        old_value = self.get_state(key)
        
        # 如果值没有变化，直接返回
        if old_value == value:
            return True
        
        # 验证新值
        if not self._validate_state_value(key, value):
            print(f"状态值验证失败: {key} = {value}")
            return False
        
        try:
            # 记录历史
            if record_history:
                self._record_state_change(key, old_value, value)
            
            # 设置新值
            if key in self._states:
                self._states[key] = value
            else:
                self._extended_states[key] = value
            
            # 更新统计
            self._update_stats(key)
            
            # 通知观察者
            if notify:
                self._notify_observers(key, old_value, value)
            
            return True
            
        except Exception as e:
            print(f"设置状态时出错: {str(e)}")
            return False
    
    def get_state(self, key, default=None):
        """
        获取状态值
        
        Args:
            key (str): 状态键
            default: 默认值
            
        Returns:
            状态值或默认值
        """
        if key in self._states:
            return self._states[key]
        elif key in self._extended_states:
            return self._extended_states[key]
        else:
            return default
    
    def get_all_states(self):
        """
        获取所有状态
        
        Returns:
            dict: 所有状态的字典
        """
        all_states = {}
        all_states.update(self._states)
        all_states.update(self._extended_states)
        return all_states.copy()
    
    def set_execution_status(self, status):
        """
        设置执行状态（便捷方法）
        
        Args:
            status (ExecutionStatus or str): 执行状态
        """
        if isinstance(status, str):
            try:
                status = ExecutionStatus(status)
            except ValueError:
                print(f"无效的执行状态: {status}")
                return False
        
        old_status = self.get_state('execution_status')
        success = self.set_state('execution_status', status)
        
        if success:
            # 根据状态设置相关的时间戳
            current_time = time.time()
            
            if status == ExecutionStatus.RUNNING:
                self.set_state('start_time', current_time, notify=False)
                self.set_state('end_time', None, notify=False)
                self.set_state('exit_code', None, notify=False)
            elif status in [ExecutionStatus.FINISHED, ExecutionStatus.STOPPED, ExecutionStatus.ERROR]:
                self.set_state('end_time', current_time, notify=False)
                self.set_state('log_completed', True, notify=False)
            
            # 发送专门的执行状态变化信号
            self.execution_status_changed.emit(
                old_status.value if old_status else None,
                status.value
            )
        
        return success
    
    def set_visibility(self, visible):
        """
        设置可见性（便捷方法）
        
        Args:
            visible (bool): 是否可见
        """
        old_visible = self.get_state('visibility')
        success = self.set_state('visibility', visible)
        
        if success:
            # 发送专门的可见性变化信号
            self.visibility_changed.emit(old_visible, visible)
        
        return success
    
    def is_running(self):
        """检查是否正在运行"""
        return self.get_state('execution_status') == ExecutionStatus.RUNNING
    
    def is_completed(self):
        """检查是否已完成"""
        status = self.get_state('execution_status')
        return status in [ExecutionStatus.FINISHED, ExecutionStatus.STOPPED, ExecutionStatus.ERROR]
    
    def get_runtime(self):
        """获取运行时间（秒）"""
        start_time = self.get_state('start_time')
        end_time = self.get_state('end_time')
        
        if start_time:
            if end_time:
                return end_time - start_time
            elif self.is_running():
                return time.time() - start_time
        
        return 0
    
    def reset_to_ready(self):
        """重置到准备状态"""
        self.set_state('execution_status', ExecutionStatus.READY, notify=False)
        self.set_state('log_completed', False, notify=False)
        self.set_state('start_time', None, notify=False)
        self.set_state('end_time', None, notify=False)
        self.set_state('exit_code', None, notify=False)
        self.set_state('process_id', None, notify=False)
        
        # 重置扩展状态
        self.set_state('last_output_time', None, notify=False)
        self.set_state('last_flush_time', None, notify=False)
        self.set_state('pending_flush', False, notify=False)
        self.set_state('flush_lock', False, notify=False)
        self.set_state('force_flush_needed', False, notify=False)
        
        # 发送重置通知
        self.state_changed.emit('reset', None, None)
    
    def rollback_to_previous_state(self):
        """回滚到上一个状态"""
        if not self._state_history:
            return False
        
        try:
            # 获取上一个状态
            previous_state = self._state_history.pop()
            
            # 恢复状态（不记录历史，避免循环）
            for key, value in previous_state['states'].items():
                self.set_state(key, value, notify=False, record_history=False)
            
            # 通知观察者状态已回滚
            self.state_changed.emit('rollback', None, previous_state)
            return True
            
        except Exception as e:
            print(f"回滚状态时出错: {str(e)}")
            return False
    
    def _validate_state_value(self, key, value):
        """验证状态值"""
        if key in self._state_constraints:
            return self._state_constraints[key](value)
        return True
    
    def _validate_execution_status(self, value):
        """验证执行状态"""
        return isinstance(value, ExecutionStatus)
    
    def _validate_boolean(self, value):
        """验证布尔值"""
        return isinstance(value, bool)
    
    def _record_state_change(self, key, old_value, new_value):
        """记录状态变化历史"""
        state_snapshot = {
            'timestamp': time.time(),
            'changed_key': key,
            'old_value': old_value,
            'new_value': new_value,
            'states': self.get_all_states()
        }
        
        self._state_history.append(state_snapshot)
        
        # 限制历史大小
        if len(self._state_history) > self._max_history_size:
            self._state_history.pop(0)
    
    def _notify_observers(self, key, old_value, new_value):
        """通知所有观察者"""
        # 发送通用状态变化信号
        self.state_changed.emit(key, old_value, new_value)
        
        # 调用观察者的回调方法
        for observer in self._observers[:]:  # 使用副本避免迭代时修改
            try:
                if hasattr(observer, 'on_state_changed'):
                    observer.on_state_changed(key, old_value, new_value)
            except Exception as e:
                print(f"通知观察者时出错: {str(e)}")
                # 移除有问题的观察者
                self.remove_observer(observer)
    
    def _update_stats(self, key):
        """更新统计信息"""
        self._stats['total_state_changes'] += 1
        self._stats['last_change_time'] = time.time()
        
        # 更新频率统计
        if key not in self._stats['state_change_frequency']:
            self._stats['state_change_frequency'][key] = 0
        self._stats['state_change_frequency'][key] += 1
    
    def get_stats(self):
        """获取统计信息"""
        return {
            'total_observers': len(self._observers),
            'history_size': len(self._state_history),
            **self._stats
        }
    
    def get_state_history(self, limit=10):
        """
        获取状态历史
        
        Args:
            limit (int): 返回的历史记录数量限制
            
        Returns:
            list: 状态历史记录
        """
        return self._state_history[-limit:] if limit > 0 else self._state_history[:]
    
    def cleanup(self):
        """清理资源 - Linux兼容版本"""
        # 防止重复清理
        if hasattr(self, '_cleaned_up') and self._cleaned_up:
            return

        try:
            # 标记为已清理，防止重复调用
            self._cleaned_up = True

            # 安全地断开所有信号连接
            self._safe_disconnect_state_signals()

            # 清空观察者（避免循环引用）
            self._observers.clear()

            # 清空历史
            self._state_history.clear()

            # 清空状态字典
            self._states.clear()
            self._extended_states.clear()

            # 重置统计
            self._stats = {
                'total_state_changes': 0,
                'state_change_frequency': {},
                'last_change_time': time.time()
            }

            # 清理锁对象
            self._lock = None

        except Exception as e:
            print(f"清理状态管理器时出错: {str(e)}")

    def _safe_disconnect_state_signals(self):
        """
        安全地断开状态信号连接 - Linux兼容版本
        使用统一的信号安全工具
        """
        # 定义需要断开的信号列表
        signals_to_disconnect = [
            'state_changed',
            'execution_status_changed',
            'visibility_changed'
        ]

        # 使用安全的信号断开工具
        safe_disconnect_multiple(self, signals_to_disconnect)
