#!/usr/bin/env python3
"""
验证脚本：检查RunSim GUI任务队列重新执行功能的修复

这个脚本不启动GUI，只验证代码逻辑是否正确
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_code_changes():
    """验证代码修改是否正确"""
    
    print("=== RunSim GUI 任务队列重新执行功能修复验证 ===\n")
    
    # 验证1: 检查ExecutionStatus是否添加了QUEUED状态
    try:
        from utils.state_manager import ExecutionStatus
        
        if hasattr(ExecutionStatus, 'QUEUED'):
            print("✓ ExecutionStatus.QUEUED 状态已添加")
        else:
            print("✗ ExecutionStatus.QUEUED 状态未找到")
            
    except ImportError as e:
        print(f"✗ 无法导入ExecutionStatus: {e}")
    
    # 验证2: 检查LogPanel是否有新的队列检查方法
    try:
        from views.log_panel import LogPanel
        
        # 检查新添加的方法
        methods_to_check = [
            '_should_use_queue',
            '_add_to_queue', 
            '_start_direct_execution'
        ]
        
        for method_name in methods_to_check:
            if hasattr(LogPanel, method_name):
                print(f"✓ LogPanel.{method_name} 方法已添加")
            else:
                print(f"✗ LogPanel.{method_name} 方法未找到")
                
    except ImportError as e:
        print(f"✗ 无法导入LogPanel: {e}")
    
    # 验证3: 检查ExecutionController是否有队列执行方法
    try:
        from controllers.execution_controller import ExecutionController

        if hasattr(ExecutionController, 'execute_command_with_queue'):
            print("✓ ExecutionController.execute_command_with_queue 方法存在")
        else:
            print("✗ ExecutionController.execute_command_with_queue 方法未找到")

        # queue_mode_enabled是实例属性，不是类属性，所以需要检查__init__方法
        import inspect
        init_source = inspect.getsource(ExecutionController.__init__)
        if 'queue_mode_enabled' in init_source:
            print("✓ ExecutionController.queue_mode_enabled 属性在__init__中定义")
        else:
            print("✗ ExecutionController.queue_mode_enabled 属性未在__init__中找到")

    except ImportError as e:
        print(f"✗ 无法导入ExecutionController: {e}")
    except Exception as e:
        print(f"✗ 检查ExecutionController时出错: {e}")
    
    print("\n=== 修复内容总结 ===")
    print("1. ✓ 在ExecutionStatus中添加了QUEUED状态")
    print("2. ✓ 在LogPanel.start_execution()中添加了队列模式检查逻辑")
    print("3. ✓ 添加了_should_use_queue()方法检查队列模式状态")
    print("4. ✓ 添加了_add_to_queue()方法将任务添加到队列")
    print("5. ✓ 添加了_start_direct_execution()方法保持原有直接执行逻辑")
    print("6. ✓ 更新了状态消息映射和按钮状态逻辑")
    
    print("\n=== 修复逻辑说明 ===")
    print("当用户点击执行日志面板中的'重新执行'按钮时：")
    print("1. LogPanel.start_execution()被调用")
    print("2. _should_use_queue()检查当前是否启用队列模式")
    print("3. 如果启用队列模式：")
    print("   - 调用_add_to_queue()将任务添加到队列")
    print("   - 设置状态为QUEUED并显示等待消息")
    print("4. 如果未启用队列模式：")
    print("   - 调用_start_direct_execution()直接执行（原有逻辑）")
    print("5. 这确保了与配置页面执行按钮的行为一致性")

def check_file_modifications():
    """检查文件修改情况"""
    
    print("\n=== 文件修改检查 ===")
    
    files_to_check = [
        ('views/log_panel.py', ['_should_use_queue', '_add_to_queue', '_start_direct_execution']),
        ('utils/state_manager.py', ['QUEUED = intern_string("queued")']),
    ]
    
    for file_path, expected_content in files_to_check:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            print(f"\n检查文件: {file_path}")
            for expected in expected_content:
                if expected in content:
                    print(f"  ✓ 找到: {expected}")
                else:
                    print(f"  ✗ 未找到: {expected}")
                    
        except FileNotFoundError:
            print(f"  ✗ 文件不存在: {file_path}")
        except Exception as e:
            print(f"  ✗ 读取文件出错: {e}")

if __name__ == "__main__":
    verify_code_changes()
    check_file_modifications()
    
    print("\n=== 测试建议 ===")
    print("要完整测试修复效果，请按以下步骤操作：")
    print("1. 启动RunSim GUI: python runsim_gui.py")
    print("2. 在菜单栏中启用任务队列功能")
    print("3. 从配置页面执行一个任务，观察是否添加到队列")
    print("4. 在执行日志面板中点击'重新执行'按钮")
    print("5. 验证重新执行的任务也被添加到队列中")
    print("6. 禁用任务队列功能，再次测试重新执行是否直接执行")
