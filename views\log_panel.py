"""
日志面板视图组件
使用模块化管理器架构，提供高性能的日志显示和处理功能
集成多种性能优化技术
"""
import os
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTextEdit, QApplication, QCheckBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon

# 导入新的管理器类
from utils.process_manager import ProcessManager
from utils.log_buffer_manager import LogBufferManager
from utils.timer_manager import TimerManager
from utils.state_manager import StateManager, ExecutionStatus
from utils.resource_monitor import ResourceMonitor, SafeResourceMonitor
from utils.string_pool import intern_string
from utils.signal_safety import safe_disconnect_multiple
from utils.integrated_memory_monitor import get_global_memory_monitor
from views.virtual_log_view import VirtualLogView


class LogPanel(QWidget):
    """
    高性能日志面板，负责UI协调和组件集成

    职责：
    1. UI界面管理和布局
    2. 各个管理器的协调
    3. 用户交互处理
    4. 信号转发和事件分发
    """

    # 定义信号
    execution_started = pyqtSignal(str)  # case_name
    execution_finished = pyqtSignal(str, int)  # case_name, exit_code
    execution_stopped = pyqtSignal(str)  # case_name

    def __init__(self, tab_name, command, parent=None):
        """
        初始化日志面板

        Args:
            tab_name (str): 标签页名称
            command (str): 执行命令
            parent (QWidget, optional): 父组件
        """
        super().__init__(parent)

        # 基本属性
        self.tab_name = tab_name
        self.command = command

        # 解析case名称
        from utils.command_generator import CommandParser
        self.case_name = CommandParser.parse_case_from_command(command) or tab_name

        # 实时模式设置
        self.realtime_mode = False  # 实时模式标志

        # 防重复执行标志
        self._execution_in_progress = False

        # 创建各个管理器
        self._create_managers()
        
        # 设置管理器之间的连接
        self._setup_manager_connections()
        
        # 初始化UI
        self._init_ui()
        
        # 启动必要的服务
        self._start_services()

    def _create_managers(self):
        """创建各个管理器实例（优化版本）"""
        # 核心管理器 - 立即创建
        self.process_manager = ProcessManager(
            command=self.command,
            working_directory=os.getcwd(),
            parent=self
        )

        self.log_buffer_manager = LogBufferManager(parent=self)
        self.timer_manager = TimerManager(parent=self)
        self.state_manager = StateManager(parent=self)

        # 非核心管理器 - 延迟创建
        self.resource_monitor = None
        self.memory_monitor = None

        # 清理状态标志
        self._cleaned_up = False

    def _setup_manager_connections(self):
        """设置管理器之间的信号连接（优化版本）"""
        # 核心信号连接
        self.process_manager.output_ready.connect(self.log_buffer_manager.append_raw_data)
        self.process_manager.process_started.connect(self._on_process_started)
        self.process_manager.process_finished.connect(self._on_process_finished)
        self.process_manager.process_stopped.connect(self._on_process_stopped)
        self.process_manager.process_error.connect(self._on_process_error)
        self.process_manager.process_paused.connect(self._on_process_paused)
        self.process_manager.process_resumed.connect(self._on_process_resumed)

        self.log_buffer_manager.log_data_ready.connect(self._on_log_data_ready)

        self.state_manager.execution_status_changed.connect(self._on_execution_status_changed)
        self.state_manager.visibility_changed.connect(self._on_visibility_changed)
        self.state_manager.add_observer(self)

    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(3)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建状态栏
        self._create_status_bar(layout)
        
        # 创建命令预览
        self._create_command_preview(layout)
        
        # 创建日志视图
        self._create_log_view(layout)

        self.setLayout(layout)

    def _create_status_bar(self, layout):
        """创建状态栏"""
        status_layout = QHBoxLayout()
        status_layout.setSpacing(5)

        # 状态标签
        self.status_label = QLabel("状态: 准备执行")
        self.status_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #2980b9;
                padding: 3px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.status_label, stretch=3)

        # 重新执行按钮
        self.re_run_btn = QPushButton("重新执行")
        self.re_run_btn.setIcon(QIcon.fromTheme("view-refresh", QIcon()))
        self.re_run_btn.clicked.connect(self.start_execution)
        self.re_run_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        status_layout.addWidget(self.re_run_btn, stretch=1)

        # 暂停按钮
        self.pause_btn = QPushButton("暂停")
        self.pause_btn.setIcon(QIcon.fromTheme("media-playback-pause", QIcon()))
        self.pause_btn.clicked.connect(self.pause_execution)
        self.pause_btn.setEnabled(False)  # 初始状态为禁用
        self.pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        status_layout.addWidget(self.pause_btn, stretch=1)

        # 继续按钮
        self.resume_btn = QPushButton("继续")
        self.resume_btn.setIcon(QIcon.fromTheme("media-playback-start", QIcon()))
        self.resume_btn.clicked.connect(self.resume_execution)
        self.resume_btn.setEnabled(False)  # 初始状态为禁用
        self.resume_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        status_layout.addWidget(self.resume_btn, stretch=1)

        # 停止按钮
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setIcon(QIcon.fromTheme("media-playback-stop", QIcon()))
        self.stop_btn.clicked.connect(self.stop_execution)
        self.stop_btn.setEnabled(False)  # 初始状态为禁用
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        status_layout.addWidget(self.stop_btn, stretch=1)

        # 实时模式复选框
        self.realtime_checkbox = QCheckBox("实时模式")
        self.realtime_checkbox.setToolTip("启用实时模式可以获得更快的日志刷新，但会消耗更多CPU资源")
        self.realtime_checkbox.stateChanged.connect(self._on_realtime_mode_changed)
        self.realtime_checkbox.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-weight: bold;
                padding: 3px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.realtime_checkbox, stretch=1)

        layout.addLayout(status_layout)

    def _create_command_preview(self, layout):
        """创建命令预览"""
        self.cmd_preview = QTextEdit()
        self.cmd_preview.setReadOnly(True)
        self.cmd_preview.setFixedHeight(60)
        self.cmd_preview.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 9pt;
                background-color: #2d2d2d;
                color: #e6e6e6;
                border: 1px solid #1a1a1a;
                border-radius: 3px;
                padding: 5px;
            }
        """)
        self.cmd_preview.setText(f"执行命令:\n{self.command}")
        layout.addWidget(self.cmd_preview)

    def _create_log_view(self, layout):
        """创建日志视图"""
        self.log_text = VirtualLogView()
        self.log_text.setStyleSheet("""
            VirtualLogView {
                font-family: "Consolas", "Courier New", monospace;
                font-size: 9pt;
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 3px;
            }
        """)
        
        # 设置字体
        from PyQt5.QtGui import QFont
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)

    def _start_services(self):
        """启动必要的服务（优化版本）"""
        # 创建日志刷新定时器（延迟创建，只在需要时创建）
        initial_interval = 25 if self.realtime_mode else 50
        self.timer_manager.create_timer(
            name="log_refresh",
            interval=initial_interval,  # 优化：根据实时模式设置初始间隔
            callback=self._refresh_log_display,
            adaptive=True  # 启用自适应，根据日志活动量动态调整
        )

    def start_execution(self):
        """开始执行用例"""
        try:
            # 防重复执行检查
            if self._execution_in_progress:
                return

            self._execution_in_progress = True

            try:
                # 检查是否启用队列模式，如果启用则添加到队列而不是直接执行
                if self._should_use_queue():
                    self._add_to_queue()
                    return

                # 直接执行模式
                self._start_direct_execution()
            finally:
                # 重置执行标志（延迟重置，避免快速重复点击）
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(1000, lambda: setattr(self, '_execution_in_progress', False))

        except Exception as e:
            print(f"启动执行时出错: {str(e)}")
            self._execution_in_progress = False
            if hasattr(self, 'state_manager') and self.state_manager:
                self.state_manager.set_execution_status(ExecutionStatus.ERROR)

    def _should_use_queue(self):
        """检查是否应该使用队列模式"""
        try:
            # 通过事件总线获取执行控制器的队列模式状态
            from utils.event_bus import EventBus
            event_bus = EventBus.instance()

            # 尝试从主窗口获取执行控制器
            app = QApplication.instance()
            if app:
                for widget in app.allWidgets():
                    if hasattr(widget, 'app_controller'):
                        app_controller = widget.app_controller
                        if hasattr(app_controller, 'execution_controller'):
                            execution_controller = app_controller.execution_controller
                            return getattr(execution_controller, 'queue_mode_enabled', False)

            return False
        except Exception as e:
            print(f"检查队列模式状态时出错: {str(e)}")
            return False

    def _add_to_queue(self):
        """将任务添加到队列"""
        try:
            # 获取执行控制器
            app = QApplication.instance()
            execution_controller = None

            if app:
                for widget in app.allWidgets():
                    if hasattr(widget, 'app_controller'):
                        app_controller = widget.app_controller
                        if hasattr(app_controller, 'execution_controller'):
                            execution_controller = app_controller.execution_controller
                            break

            if execution_controller:
                # 使用执行控制器的队列执行方法
                execution_controller.execute_command_with_queue(self.command, self.case_name)

                # 更新状态显示
                if hasattr(self, 'state_manager') and self.state_manager:
                    self.state_manager.set_execution_status(ExecutionStatus.QUEUED)

                if hasattr(self, 'log_text') and self.log_text:
                    self.log_text.clear()
                    self.log_text.append("任务已添加到队列，等待执行...")

                # 显示命令预览
                if hasattr(self, 'cmd_preview') and self.cmd_preview:
                    self.cmd_preview.clear()
                    command_text = intern_string(f"队列任务命令:\n{self.command}")
                    self.cmd_preview.setText(command_text)
            else:
                # 如果无法获取执行控制器，回退到直接执行
                print("无法获取执行控制器，回退到直接执行模式")
                self._start_direct_execution()

        except Exception as e:
            print(f"添加任务到队列时出错: {str(e)}")
            # 出错时回退到直接执行
            self._start_direct_execution()

    def _start_direct_execution(self):
        """直接执行模式（原有逻辑）"""
        try:
            # 更新状态
            if hasattr(self, 'state_manager') and self.state_manager:
                self.state_manager.set_execution_status(ExecutionStatus.RUNNING)

            # 清空日志显示
            if hasattr(self, 'log_text') and self.log_text:
                self.log_text.clear()

            # 清空日志缓冲区（检查是否存在）
            if hasattr(self, 'log_buffer_manager') and self.log_buffer_manager:
                self.log_buffer_manager.clear_buffers()

            # 重新显示命令
            if hasattr(self, 'cmd_preview') and self.cmd_preview:
                self.cmd_preview.clear()
                command_text = intern_string(f"执行命令:\n{self.command}")
                self.cmd_preview.setText(command_text)

            # 设置自动滚动
            if hasattr(self, 'log_text') and self.log_text and hasattr(self.log_text, 'setAutoScroll'):
                self.log_text.setAutoScroll(True)

            # 启动进程
            if hasattr(self, 'process_manager') and self.process_manager and self.process_manager.start():
                # 启动日志刷新定时器
                if hasattr(self, 'timer_manager') and self.timer_manager:
                    self.timer_manager.start_timer("log_refresh")

                # 发出执行开始信号
                self.execution_started.emit(self.case_name)
            else:
                # 启动失败，恢复状态
                if hasattr(self, 'state_manager') and self.state_manager:
                    self.state_manager.set_execution_status(ExecutionStatus.ERROR)

        except Exception as e:
            print(f"直接执行时出错: {str(e)}")
            if hasattr(self, 'state_manager') and self.state_manager:
                self.state_manager.set_execution_status(ExecutionStatus.ERROR)

    def pause_execution(self):
        """暂停进程执行"""
        try:
            # 暂停进程
            if self.process_manager.pause():
                # 暂停日志刷新定时器
                if hasattr(self, 'timer_manager') and self.timer_manager:
                    self.timer_manager.stop_timer("log_refresh")

                # 暂停日志缓冲处理
                if hasattr(self, 'log_buffer_manager') and self.log_buffer_manager:
                    self.log_buffer_manager.pause_processing()

                # 更新状态
                self.state_manager.set_execution_status(ExecutionStatus.PAUSED)

                # 添加暂停提示
                self.log_text.append("\n[执行已暂停 - 仿真器进入交互模式]\n")
                self.log_text.append("[提示: 点击'继续'按钮恢复执行]\n")
                self.log_text.append("[日志输出已暂停]\n")

        except Exception as e:
            print(f"暂停执行时出错: {str(e)}")
            self.log_text.append(f"\n[暂停进程时出错: {str(e)}]\n")

    def resume_execution(self):
        """继续进程执行"""
        try:
            # 继续进程
            if self.process_manager.resume():
                # 恢复日志缓冲处理
                if hasattr(self, 'log_buffer_manager') and self.log_buffer_manager:
                    self.log_buffer_manager.resume_processing()

                # 重新启动日志刷新定时器
                if hasattr(self, 'timer_manager') and self.timer_manager:
                    self.timer_manager.start_timer("log_refresh")

                # 更新状态
                self.state_manager.set_execution_status(ExecutionStatus.RUNNING)

                # 添加继续提示
                self.log_text.append("\n[执行已继续]\n")
                self.log_text.append("[日志输出已恢复]\n")

        except Exception as e:
            print(f"继续执行时出错: {str(e)}")
            self.log_text.append(f"\n[继续进程时出错: {str(e)}]\n")

    def stop_execution(self):
        """停止进程执行"""
        try:
            # 停止进程
            if self.process_manager.stop():
                # 更新状态
                self.state_manager.set_execution_status(ExecutionStatus.STOPPED)

                # 添加停止提示
                self.log_text.append("\n[执行已终止]\n")

                # 发出执行停止信号
                self.execution_stopped.emit(self.case_name)

        except Exception as e:
            print(f"停止执行时出错: {str(e)}")
            self.log_text.append(f"\n[终止进程时出错: {str(e)}]\n")

    def _on_process_started(self):
        """进程启动处理"""
        # 检查是否已经清理
        if getattr(self, '_cleaned_up', False):
            return

        # 设置进程ID
        if hasattr(self, 'process_manager') and self.process_manager:
            process_id = self.process_manager.get_process_id()
            if hasattr(self, 'state_manager') and self.state_manager:
                self.state_manager.set_state('process_id', process_id)

        # 更新按钮状态
        self._update_button_states()

    def _on_process_finished(self, exit_code):
        """进程结束处理"""
        # 检查是否已经清理
        if getattr(self, '_cleaned_up', False):
            return

        try:
            # 停止定时器
            if hasattr(self, 'timer_manager') and self.timer_manager:
                self.timer_manager.stop_timer("log_refresh")

            # 强制刷新剩余日志
            if hasattr(self, 'log_buffer_manager') and self.log_buffer_manager:
                self.log_buffer_manager.force_flush()

            # 更新状态
            if hasattr(self, 'state_manager') and self.state_manager:
                self.state_manager.set_state('exit_code', exit_code)

                if exit_code == 0:
                    self.state_manager.set_execution_status(ExecutionStatus.FINISHED)
                    success_msg = intern_string("\n[执行完成] 可以使用工具栏按钮查看波形或日志\n")
                    if hasattr(self, 'log_text') and self.log_text:
                        self.log_text.append(success_msg)
                else:
                    self.state_manager.set_execution_status(ExecutionStatus.ERROR)
                    error_msg = intern_string(f"\n[执行失败] 退出码: {exit_code}\n")
                    if hasattr(self, 'log_text') and self.log_text:
                        self.log_text.append(error_msg)

            # 确保滚动到底部
            if hasattr(self, 'log_text') and self.log_text:
                self.log_text.setAutoScroll(True)

            # 发出执行完成信号
            self.execution_finished.emit(self.case_name, exit_code)

        except Exception as e:
            print(f"处理进程结束时出错: {str(e)}")
            if hasattr(self, 'log_text') and self.log_text:
                self.log_text.append(f"\n[错误] 处理进程结束时发生错误: {str(e)}\n")

    def _on_process_stopped(self):
        """进程停止处理"""
        # 检查是否已经清理
        if getattr(self, '_cleaned_up', False):
            return

        if hasattr(self, 'timer_manager') and self.timer_manager:
            self.timer_manager.stop_timer("log_refresh")

        # 更新按钮状态
        self._update_button_states()

    def _on_process_error(self, error_message):
        """进程错误处理"""
        # 检查是否已经清理
        if getattr(self, '_cleaned_up', False):
            return

        if hasattr(self, 'state_manager') and self.state_manager:
            self.state_manager.set_execution_status(ExecutionStatus.ERROR)
        error_msg = intern_string(f"\n[进程错误] {error_message}\n")
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.append(error_msg)

    def _on_process_paused(self):
        """进程暂停处理"""
        # 检查是否已经清理
        if getattr(self, '_cleaned_up', False):
            return

        # 更新按钮状态
        self._update_button_states()

    def _on_process_resumed(self):
        """进程继续处理"""
        # 检查是否已经清理
        if getattr(self, '_cleaned_up', False):
            return

        # 更新按钮状态
        self._update_button_states()

    def _on_log_data_ready(self, log_text):
        """日志数据准备好处理"""
        if log_text:
            self.log_text.append(log_text)

    def _refresh_log_display(self):
        """刷新日志显示（定时器回调）"""
        # 这个方法由定时器调用，用于定期检查和刷新日志
        # 实际的日志数据通过信号机制处理，这里主要用于UI更新
        pass

    def _on_execution_status_changed(self, old_status, new_status):
        """执行状态变化处理"""
        status_messages = {
            ExecutionStatus.READY.value: intern_string("状态: 准备执行"),
            ExecutionStatus.RUNNING.value: intern_string("状态: 执行中..."),
            ExecutionStatus.PAUSED.value: intern_string("状态: 已暂停"),
            ExecutionStatus.FINISHED.value: intern_string("状态: 执行完成"),
            ExecutionStatus.STOPPED.value: intern_string("状态: 已终止"),
            ExecutionStatus.ERROR.value: intern_string("状态: 执行失败"),
            ExecutionStatus.QUEUED.value: intern_string("状态: 队列等待中...")
        }

        message = status_messages.get(new_status, f"状态: {new_status}")
        self.status_label.setText(message)

        # 更新按钮状态
        self._update_button_states()

    def _update_button_states(self):
        """根据当前状态更新按钮状态"""
        if not hasattr(self, 'state_manager') or not self.state_manager:
            return

        current_status = self.state_manager.get_state('execution_status')
        is_running = self.process_manager.is_running() if hasattr(self, 'process_manager') and self.process_manager else False
        is_paused = self.process_manager.is_paused() if hasattr(self, 'process_manager') and self.process_manager else False
        is_queued = current_status == ExecutionStatus.QUEUED

        # 重新执行按钮：在非运行状态且非队列状态时才启用
        self.re_run_btn.setEnabled(not is_running and not is_queued)

        # 暂停按钮：只有在运行且未暂停时才启用
        self.pause_btn.setEnabled(is_running and not is_paused)

        # 继续按钮：只有在运行且已暂停时才启用
        self.resume_btn.setEnabled(is_running and is_paused)

        # 停止按钮：只有在运行时才启用
        self.stop_btn.setEnabled(is_running)

    def _on_visibility_changed(self, old_visible, new_visible):
        """可见性变化处理"""
        # 检查是否已经清理
        if getattr(self, '_cleaned_up', False):
            return

        # 根据可见性调整刷新频率
        if hasattr(self, 'timer_manager') and self.timer_manager and self.timer_manager.is_timer_active("log_refresh"):
            if new_visible:
                # 可见时根据实时模式调整频率
                interval = 25 if self.realtime_mode else 50
                self.timer_manager.adjust_timer_interval("log_refresh", interval)
            else:
                # 不可见时使用较低频率，节省资源
                self.timer_manager.adjust_timer_interval("log_refresh", 500)

    def _on_realtime_mode_changed(self, state):
        """实时模式切换处理"""
        # 检查是否已经清理
        if getattr(self, '_cleaned_up', False):
            return

        self.realtime_mode = state == 2  # Qt.Checked = 2

        # 如果当前标签页可见且定时器活跃，立即调整刷新频率
        if (hasattr(self, 'state_manager') and self.state_manager and
            hasattr(self, 'timer_manager') and self.timer_manager and
            self.state_manager.get_state('visible', True) and
            self.timer_manager.is_timer_active("log_refresh")):

            if self.realtime_mode:
                # 实时模式：使用更高频率
                self.timer_manager.adjust_timer_interval("log_refresh", 25)
                # 同时调整日志视图的批处理设置
                if hasattr(self, 'log_text') and hasattr(self.log_text, '_batch_timeout'):
                    self.log_text._batch_timeout = 5  # 5ms超时
                    self.log_text._batch_size = 10   # 更小的批处理大小
            else:
                # 普通模式：使用标准频率
                self.timer_manager.adjust_timer_interval("log_refresh", 50)
                # 恢复标准批处理设置
                if hasattr(self, 'log_text') and hasattr(self.log_text, '_batch_timeout'):
                    self.log_text._batch_timeout = 10  # 10ms超时
                    self.log_text._batch_size = 20    # 标准批处理大小

    # 移除资源监控相关方法以提升性能

    def on_state_changed(self, key, old_value, new_value):
        """状态变化观察者回调"""
        # 这里可以处理其他状态变化
        if key == 'auto_scroll':
            self.log_text.setAutoScroll(new_value)

    def showEvent(self, event):
        """标签页显示时的处理"""
        super().showEvent(event)
        # 检查是否已经清理，避免在清理后调用
        if hasattr(self, 'state_manager') and self.state_manager and not getattr(self, '_cleaned_up', False):
            self.state_manager.set_visibility(True)

    def hideEvent(self, event):
        """标签页隐藏时的处理"""
        super().hideEvent(event)
        # 检查是否已经清理，避免在清理后调用
        if hasattr(self, 'state_manager') and self.state_manager and not getattr(self, '_cleaned_up', False):
            self.state_manager.set_visibility(False)

    def set_visible(self, visible):
        """设置tab可见性"""
        # 检查是否已经清理，避免在清理后调用
        if hasattr(self, 'state_manager') and self.state_manager and not getattr(self, '_cleaned_up', False):
            self.state_manager.set_visibility(visible)

    def get_stats(self):
        """获取统计信息（简化版本）"""
        # 如果已经清理，返回空统计信息
        if getattr(self, '_cleaned_up', False):
            return {
                'process_manager': {'is_running': False, 'runtime': 0, 'process_id': None},
                'log_buffer_manager': {},
                'timer_manager': {},
                'state_manager': {}
            }

        stats = {}

        # 安全获取各个管理器的统计信息
        if hasattr(self, 'process_manager') and self.process_manager:
            try:
                stats['process_manager'] = {
                    'is_running': self.process_manager.is_running(),
                    'runtime': self.process_manager.get_runtime(),
                    'process_id': self.process_manager.get_process_id()
                }
            except Exception:
                stats['process_manager'] = {'is_running': False, 'runtime': 0, 'process_id': None}
        else:
            stats['process_manager'] = {'is_running': False, 'runtime': 0, 'process_id': None}

        if hasattr(self, 'log_buffer_manager') and self.log_buffer_manager:
            try:
                stats['log_buffer_manager'] = self.log_buffer_manager.get_buffer_stats()
            except Exception:
                stats['log_buffer_manager'] = {}
        else:
            stats['log_buffer_manager'] = {}

        if hasattr(self, 'timer_manager') and self.timer_manager:
            try:
                stats['timer_manager'] = self.timer_manager.get_stats()
            except Exception:
                stats['timer_manager'] = {}
        else:
            stats['timer_manager'] = {}

        if hasattr(self, 'state_manager') and self.state_manager:
            try:
                stats['state_manager'] = self.state_manager.get_stats()
            except Exception:
                stats['state_manager'] = {}
        else:
            stats['state_manager'] = {}

        return stats

    def cleanup(self):
        """清理资源（优化版本）"""
        # 防止重复清理
        if hasattr(self, '_cleaned_up') and self._cleaned_up:
            return

        try:
            # 标记为已清理，防止重复调用
            self._cleaned_up = True

            # 断开信号连接，避免循环引用
            self._disconnect_signals()

            # 停止进程
            if hasattr(self, 'process_manager') and self.process_manager:
                if self.process_manager.is_running():
                    self.process_manager.stop()
                self.process_manager.cleanup()
                self.process_manager = None

            # 停止所有定时器
            if hasattr(self, 'timer_manager') and self.timer_manager:
                try:
                    self.timer_manager.stop_all_timers()
                    self.timer_manager.cleanup()
                except Exception as e:
                    print(f"清理定时器管理器时出错: {str(e)}")
                finally:
                    self.timer_manager = None

            # 强制刷新剩余日志并清理
            if hasattr(self, 'log_buffer_manager') and self.log_buffer_manager:
                self.log_buffer_manager.force_flush()
                self.log_buffer_manager.cleanup()
                self.log_buffer_manager = None

            # 清理状态管理器
            if hasattr(self, 'state_manager') and self.state_manager:
                self.state_manager.cleanup()
                self.state_manager = None

            # 清理资源监控器
            if hasattr(self, 'resource_monitor'):
                self.resource_monitor = None

            # 清理内存监控器引用
            if hasattr(self, 'memory_monitor'):
                self.memory_monitor = None

            # 清理UI组件引用
            self._cleanup_ui_references()

            # 清理基本属性，避免循环引用
            self.tab_name = None
            self.command = None
            self.case_name = None

            # 强制垃圾回收
            import gc
            gc.collect()

            # 强制处理Qt事件，确保deleteLater生效
            # 安全地处理QApplication实例
            try:
                from PyQt5.QtWidgets import QApplication
                app_instance = QApplication.instance()
                if app_instance:
                    app_instance.processEvents()
            except Exception as e:
                print(f"处理Qt事件时出错: {str(e)}")

        except Exception as e:
            print(f"清理LogPanel时出错: {str(e)}")
            # 确保关键对象被清理
            self.process_manager = None
            self.timer_manager = None
            self.log_buffer_manager = None
            self.state_manager = None

    def _disconnect_signals(self):
        """断开所有信号连接 - 使用安全信号工具"""
        from utils.signal_safety import safe_disconnect

        try:
            # 使用安全信号工具断开ProcessManager信号
            if (hasattr(self, 'process_manager') and self.process_manager and
                not getattr(self.process_manager, '_cleaned_up', False)):
                # 只有在ProcessManager未清理时才断开信号，避免重复断开
                safe_disconnect(self.process_manager.output_ready, 'output_ready')
                safe_disconnect(self.process_manager.process_started, 'process_started')
                safe_disconnect(self.process_manager.process_finished, 'process_finished')
                safe_disconnect(self.process_manager.process_stopped, 'process_stopped')
                safe_disconnect(self.process_manager.process_error, 'process_error')
                safe_disconnect(self.process_manager.process_paused, 'process_paused')
                safe_disconnect(self.process_manager.process_resumed, 'process_resumed')
        except Exception:
            pass

        # 断开其他管理器的信号连接
        try:
            # LogBufferManager信号
            if hasattr(self, 'log_buffer_manager') and self.log_buffer_manager:
                safe_disconnect(self.log_buffer_manager.log_data_ready, 'log_data_ready')
        except Exception:
            pass

        try:
            # StateManager信号
            if hasattr(self, 'state_manager') and self.state_manager:
                safe_disconnect(self.state_manager.execution_status_changed, 'execution_status_changed')
                safe_disconnect(self.state_manager.visibility_changed, 'visibility_changed')
                try:
                    self.state_manager.remove_observer(self)
                except Exception:
                    pass
        except Exception:
            pass

        # 资源监控器和内存监控器的信号断开
        try:
            if hasattr(self, 'resource_monitor') and self.resource_monitor:
                safe_disconnect(self.resource_monitor.resources_updated, 'resources_updated')
        except Exception:
            pass

        try:
            if hasattr(self, 'memory_monitor') and self.memory_monitor:
                safe_disconnect(self.memory_monitor.memory_alert, 'memory_alert')
                safe_disconnect(self.memory_monitor.memory_stats_updated, 'memory_stats_updated')
        except Exception:
            pass

    def _cleanup_ui_references(self):
        """清理UI组件引用"""
        from utils.signal_safety import safe_disconnect

        try:
            # 清理按钮连接
            if hasattr(self, 're_run_btn') and self.re_run_btn:
                try:
                    safe_disconnect(self.re_run_btn.clicked, 're_run_btn.clicked')
                    self.re_run_btn.deleteLater()
                except Exception:
                    pass
            if hasattr(self, 'stop_btn') and self.stop_btn:
                try:
                    safe_disconnect(self.stop_btn.clicked, 'stop_btn.clicked')
                    self.stop_btn.deleteLater()
                except Exception:
                    pass
            if hasattr(self, 'realtime_checkbox') and self.realtime_checkbox:
                try:
                    safe_disconnect(self.realtime_checkbox.stateChanged, 'realtime_checkbox.stateChanged')
                    self.realtime_checkbox.deleteLater()
                except Exception:
                    pass

            # 清理其他UI组件
            if hasattr(self, 'status_label') and self.status_label:
                try:
                    self.status_label.deleteLater()
                except Exception:
                    pass

            if hasattr(self, 'cmd_preview') and self.cmd_preview:
                try:
                    self.cmd_preview.deleteLater()
                except Exception:
                    pass

            if hasattr(self, 'log_text') and self.log_text:
                try:
                    self.log_text.deleteLater()
                except Exception:
                    pass

            # 清理UI组件引用
            self.status_label = None
            self.re_run_btn = None
            self.stop_btn = None
            self.realtime_checkbox = None
            self.cmd_preview = None
            self.log_text = None

        except Exception as e:
            print(f"清理UI引用时出错: {str(e)}")

    def cleanup_async(self):
        """异步清理方法"""
        # 防止重复清理
        if hasattr(self, '_cleaned_up') and self._cleaned_up:
            return

        try:
            # 标记为已清理，防止重复调用
            self._cleaned_up = True

            # 检查应用程序是否正在关闭
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import QThread, QTimer

            app_instance = QApplication.instance()
            if app_instance is None or app_instance.closingDown():
                # 如果应用程序正在关闭，直接执行同步清理，避免延迟
                self.cleanup()
                return

            if QThread.currentThread() != app_instance.thread():
                # 如果不在主线程且应用程序未关闭，使用QTimer.singleShot在主线程中执行
                QTimer.singleShot(0, self._cleanup_in_main_thread)
                return

            self._cleanup_in_main_thread()

        except Exception as e:
            print(f"异步清理重构后的LogPanel时出错: {str(e)}")

    def _cleanup_in_main_thread(self):
        """在主线程中执行清理操作"""
        try:
            # 立即断开信号连接
            self._disconnect_signals()

            # 立即停止定时器（确保在主线程中）
            if hasattr(self, 'timer_manager') and self.timer_manager:
                self.timer_manager.stop_all_timers()

            # 立即停止进程（不等待）
            if hasattr(self, 'process_manager') and self.process_manager and self.process_manager.is_running():
                self.process_manager.stop()

            # 检查应用程序是否正在关闭
            from PyQt5.QtWidgets import QApplication
            app_instance = QApplication.instance()
            if app_instance is None or app_instance.closingDown():
                # 如果应用程序正在关闭，立即执行所有清理操作，避免延迟
                self._delayed_cleanup()
            else:
                # 否则使用QTimer延迟执行其他清理操作
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(10, self._delayed_cleanup)

        except Exception as e:
            print(f"在主线程清理LogPanel时出错: {str(e)}")

    def _delayed_cleanup(self):
        """延迟清理操作"""
        try:
            # 清理各个管理器
            if hasattr(self, 'log_buffer_manager') and self.log_buffer_manager:
                self.log_buffer_manager.cleanup()
            if hasattr(self, 'state_manager') and self.state_manager:
                self.state_manager.cleanup()
            if hasattr(self, 'process_manager') and self.process_manager:
                self.process_manager.cleanup()
            if hasattr(self, 'timer_manager') and self.timer_manager:
                self.timer_manager.cleanup()

            # 停止资源监控
            if hasattr(self, 'resource_monitor') and self.resource_monitor:
                try:
                    self.resource_monitor.stop_monitoring()
                except Exception:
                    pass

            # 清理UI引用
            self._cleanup_ui_references()

        except Exception as e:
            print(f"延迟清理时出错: {str(e)}")

    def __del__(self):
        """析构函数"""
        try:
            # 只在未清理时才调用cleanup
            if not hasattr(self, '_cleaned_up') or not self._cleaned_up:
                self.cleanup()
        except Exception:
            # 忽略析构函数中的错误
            pass
